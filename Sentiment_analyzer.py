# Gap detection
def calculate_gap(df):
    prev_close = df['close'].shift(1).iloc[-1]
    current_open = df['open'].iloc[-1]
    if current_open > prev_close:
        return 'gap_up'
    elif current_open < prev_close:
        return 'gap_down'
    return 'no_gap'

# Overall sentiment
def get_market_sentiment(gap_type, first_candle_sentiment):
    if gap_type == 'gap_up':
        sentiment = 'bullish'
    elif gap_type == 'gap_down':
        sentiment = 'bearish'
    else:
        sentiment = first_candle_sentiment
    
    # Handle cases (e.g., volatile)
    is_volatile = False  # Check big first candle or half-day trends
    if is_volatile:
        return sentiment, True  # Allow both sides
    return sentiment, False

# Half-day checks
def is_first_half(timestamp):
    return timestamp.hour < 12 or (timestamp.hour == 12 and timestamp.minute <= 30)

# Gap persistence (streak tracking)
gap_streak = 0  # Positive for up streak
def update_gap_streak(gap_type):
    global gap_streak
    if gap_type == 'gap_up':
        gap_streak = max(gap_streak + 1, 1)
    elif gap_type == 'gap_down':
        gap_streak = min(gap_streak - 1, -1)
    # On CHOCH reversal, reset or switch