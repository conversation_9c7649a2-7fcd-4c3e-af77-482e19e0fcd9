# Section 0: Separate Manual Customizer
# This is a dedicated configuration dictionary or file for manual tweaks.
# Users can edit these values separately (e.g., in config.py) without touching core logic.
# Load via: from config import CUSTOM_CONFIG  (or json.load if using a file)
# All functions below reference CUSTOM_CONFIG[key] for dynamic values.

CUSTOM_CONFIG = {
    # Market Perspective
    'bullish_leg_points': 140,  # Expected points in bullish moves; adjust based on backtests
    'bearish_leg_points': 200,  # Expected points in bearish moves
    'key_move_times': [(13, 0), (13, 15), (13, 30), (14, 0), (14, 30), (14, 56)],  # List of (hour, min) for key times; add/remove as needed
    'volatility_factor': 1.5,  # Multiplier for expiry/high-vol days; set to 1.0 for normal

    # EQH/EQL and RR
    'eqh_eql_risk_points': 5,   # Risk for EQH/EQL trades
    'eqh_eql_reward_points': 10,  # Reward for EQH/EQL
    'default_risk_points': 10,    # Default risk outside EQH/EQL
    'default_reward_points': 20,  # Default reward
    'atr_period': 200,            # ATR period for detections
    'tolerance_factor': 0.1,      # For EQH/EQL equality check
    'near_threshold_multiplier': 0.5,  # For OB/FVG "near" (x * ATR)

    # Trade Management
    'min_capture_points': 60,     # Minimum profit target per trade
    'trail_amount': 2,            # Points to trail SL after 60
    'extra_trail_after_target': 5,  # For 5:10 RR, trail after +5 more points

    # Other Filters
    'first_half_end_hour': 12,    # Hour to define first half (e.g., 12:30)
    'first_half_end_minute': 30,
    'big_candle_threshold': 50    # Points for "big first candle" to flag volatile
}

# Function to load/override config (manual customizer entry point)
def load_custom_config(file_path='config.json'):  # Or .py/.yaml
    import json
    try:
        with open(file_path, 'r') as f:
            overrides = json.load(f)
        CUSTOM_CONFIG.update(overrides)  # Merge manual changes
    except FileNotFoundError:
        pass  # Use defaults
    return CUSTOM_CONFIG

# Call at script start: config = load_custom_config()

# Now, integrate into existing sections (reference config keys)

# Section 1: Market Perspective and Move Sizes
BULLISH_LEG_POINTS = CUSTOM_CONFIG['bullish_leg_points']
BEARISH_LEG_POINTS = CUSTOM_CONFIG['bearish_leg_points']
KEY_MOVE_TIMES = CUSTOM_CONFIG['key_move_times']

def is_expiry_day(current_date):
    # As before; multiply stops by config['volatility_factor'] if True
    return False  # Customizable via external calendar file if needed

def calculate_first_candle_sentiment(df):
    # As before

def is_interval_start(timestamp):
    # As before

# Section 2: EQH/EQL Detection and Trade Bias
def detect_eqh_eql(df, atr_period=CUSTOM_CONFIG['atr_period'], tolerance_factor=CUSTOM_CONFIG['tolerance_factor']):
    # As before, using configurable periods/factors

def get_trade_bias_on_eqh_eql(detection, sentiment, choch_type):
    # As before

def calculate_sl_tp(entry_price, bias, is_eqh_eql=False):
    if is_eqh_eql:
        risk = CUSTOM_CONFIG['eqh_eql_risk_points']
        reward = CUSTOM_CONFIG['eqh_eql_reward_points']
    else:
        risk = CUSTOM_CONFIG['default_risk_points']
        reward = CUSTOM_CONFIG['default_reward_points']
    if bias == 'buy':
        sl = entry_price - risk
        tp = entry_price + reward
    elif bias == 'sell':
        sl = entry_price + risk
        tp = entry_price - reward
    return sl, tp

# Section 3: Additional Filters and Biases
CURRENT_ATR = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14).iloc[-1]
NEAR_THRESHOLD = CURRENT_ATR * CUSTOM_CONFIG['near_threshold_multiplier']

def get_ob_fvg_bias(current_price, bullish_ob_levels, bearish_ob_levels, fvg_levels):
    # As before, using configurable threshold

def avoid_against_interchange(bias, current_price):
    # As before

# Section 4: Gap and Sentiment Analysis
def calculate_gap(df):
    # As before

def get_market_sentiment(gap_type, first_candle_sentiment):
    # As before; add check for big first candle
    first_candle_size = abs(df['close'].iloc[0] - df['open'].iloc[0])
    is_volatile = first_candle_size > CUSTOM_CONFIG['big_candle_threshold']
    # Adjust sentiment/allow both sides if volatile

def is_first_half(timestamp):
    return timestamp.hour < CUSTOM_CONFIG['first_half_end_hour'] or \
           (timestamp.hour == CUSTOM_CONFIG['first_half_end_hour'] and timestamp.minute <= CUSTOM_CONFIG['first_half_end_minute'])

# Gap streak as before

# Section 5: Trade Management and Trailing
MIN_CAPTURE_POINTS = CUSTOM_CONFIG['min_capture_points']

def manage_trade(entry_price, bias, current_price, initial_sl, initial_tp, is_eqh_eql=False):
    profit_points = (current_price - entry_price) if bias == 'buy' else (entry_price - current_price)
    
    # At initial reward
    reward_threshold = (initial_tp - entry_price) if bias == 'buy' else (entry_price - initial_tp)
    if profit_points >= reward_threshold:
        initial_sl = entry_price  # Breakeven
    
    # To min capture
    if profit_points >= MIN_CAPTURE_POINTS:
        trail_amount = CUSTOM_CONFIG['trail_amount']
        if bias == 'buy':
            initial_sl = current_price - (entry_price - initial_sl) - trail_amount  # Dynamic trail
        else:
            initial_sl = current_price + (initial_sl - entry_price) + trail_amount
    
    # For EQH/EQL (5:10 RR) extra trail
    if is_eqh_eql and profit_points >= reward_threshold + CUSTOM_CONFIG['extra_trail_after_target']:
        # Intensify trailing
    
    # Exit check
    if (bias == 'buy' and current_price < initial_sl) or (bias == 'sell' and current_price > initial_sl):
        return 'exit', profit_points
    
    return 'hold', profit_points

# Backtest loop as before, pulling from CUSTOM_CONFIG