# ATR for "near" (e.g., for OB/FVG)
CURRENT_ATR = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14).iloc[-1]  # Or 200

# OB/FVG bias (assume detection functions from Pine)
def get_ob_fvg_bias(current_price, bullish_ob_levels, bearish_ob_levels, fvg_levels):
    near_threshold = CURRENT_ATR * 0.5
    if any(abs(current_price - level) < near_threshold for level in bullish_ob_levels + [f['top'] for f in fvg_levels if f['bull']]):
        return 'buy'
    elif any(abs(current_price - level) < near_threshold for level in bearish_ob_levels + [f['btm'] for f in fvg_levels if not f['bull']]):
        return 'sell'
    return None

# Support/Resistance interchange (track broken levels)
broken_supports = []  # List of levels
def avoid_against_interchange(bias, current_price):
    if bias == 'buy' and current_price < max(broken_supports):  # Below resistance
        return False  # Avoid
    return True

# Default RR
DEFAULT_RISK_POINTS = 10
DEFAULT_REWARD_POINTS = 20