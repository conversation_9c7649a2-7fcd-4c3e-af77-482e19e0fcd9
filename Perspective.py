# Define constants for expected moves
BULLISH_LEG_POINTS = 140  # Average points in bullish move
BEARISH_LEG_POINTS = 200  # Average points in bearish move

# Key move times as list of (hour, minute) tuples (assuming IST; convert timestamps accordingly)
KEY_MOVE_TIMES = [(13, 0), (13, 15), (13, 30), (14, 0), (14, 30), (14, 56)]

# Expiry day flag (implement as function to check date against expiry calendar)
def is_expiry_day(current_date):
    # Query or hardcode expiry dates; return True/False
    # Example: if current_date.weekday() == 3:  # Thursday for Nifty weekly
    return False  # Placeholder

# Volatility factor for expiry
VOLATILITY_FACTOR = 1.5 if is_expiry_day(df['date'].iloc[-1]) else 1.0  # Multiply stops/targets if True

# First candle sentiment
def calculate_first_candle_sentiment(df):
    first_candle = df.iloc[0]  # Assuming df starts at market open (09:15)
    if first_candle['close'] > first_candle['open']:
        return 'bullish'
    elif first_candle['close'] < first_candle['open']:
        return 'bearish'
    else:
        return 'neutral'

# Interval starts filter
def is_interval_start(timestamp):
    minute = timestamp.minute
    return minute == 0 or minute == 30  # Prioritize entries here