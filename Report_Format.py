# Report generation pseudocode
def generate_daily_report(trade_log, df):
    current_date = df['date'].iloc[-1].strftime('%Y-%m-%d')  # e.g., 2025-09-23
    last_row = df.iloc[-1]
    opening = df['open'].iloc[0]  # First candle of day
    closing = last_row['close']
    high = last_row['high']
    low = last_row['low']
    range_val = high - low
    
    # First candle sentiment (from earlier function)
    first_candle_sentiment = calculate_first_candle_sentiment(df)
    gap_type = calculate_gap(df)
    recommended_bias = get_market_sentiment(gap_type, first_candle_sentiment)[0]  # Bias only
    
    # Signal summary (hypothetical or from model stats)
    total_signals = len([s for s in trade_log if s['signal'] in ['EQH', 'EQL']])  # Adjust with filter logic
    passed_timing = total_signals  # Placeholder; add filter for key times
    gap_aligned = total_signals  # Placeholder; add gap alignment check
    trades_taken = len(trade_log)
    
    # Trade details (live perspective, only today’s trades)
    trade_section = ""
    for i, trade in enumerate(trade_log, 1):
        if trade['entry_time'].date() == pd.to_datetime(current_date).date():
            trade_section += f"   Trade #{i}:\n"
            trade_section += f"   📋 Signal: {trade['signal']}\n"
            trade_section += f"   ⏰ Entry Time: {trade['entry_time'].strftime('%H:%M')}\n"
            trade_section += f"   💰 Entry Price: {trade['entry_price']}\n"
            trade_section += f"   📊 Direction: {trade['direction']}\n"
            trade_section += f"   🎯 Risk/Reward: {trade['risk_reward']}\n"
            trade_section += f"   ✅ Outcome: {trade['outcome']}\n"
            trade_section += f"   🚪 Exit Time: {trade['exit_time'].strftime('%H:%M') if trade['exit_time'] else 'N/A'}\n"
            trade_section += f"   💸 Exit Price: {trade['exit_price'] if trade['exit_price'] else 'N/A'}\n"
            trade_section += f"   💰 P&L: {trade['pnl'] if trade['pnl'] else '0'}\n"
            trade_section += f"   📝 Exit Reason: {trade['exit_reason'] if trade['exit_reason'] else 'Open'}\n"

    # Daily summary
    wins = len([t for t in trade_log if t['outcome'] == 'Win'])
    losses = len([t for t in trade_log if t['outcome'] == 'Loss'])
    win_rate = (wins / trades_taken * 100) if trades_taken > 0 else 0
    daily_pnl = sum(t['pnl'] for t in trade_log if t['pnl']) if trades_taken > 0 else 0
    profitable_day = "📈 PROFITABLE DAY! 🎉" if daily_pnl > 0 else ""

    # Assemble report
    report = f"DAILY REPORT: {current_date}\n\n"
    report += "📊 MARKET OVERVIEW:\n"
    report += f"   Opening: {opening}\n"
    report += f"   Closing: {closing}\n"
    report += f"   High: {high}\n"
    report += f"   Low: {low}\n"
    report += f"   Range: {range_val}\n\n"
    report += "📈 GAP ANALYSIS:\n"
    report += f"   First Candle Sentiment:  {first_candle_sentiment}\n"
    report += f"   Recommended Bias: {recommended_bias}\n"
    report += f"   Candle Range: {abs(opening - closing)}\n\n"
    report += "🎯 SIGNAL SUMMARY:\n"
    report += f"   Total Signals Generated: {total_signals}\n"
    report += f"   Passed Timing Filters: {passed_timing}\n"
    report += f"   Gap-Aligned Signals: {gap_aligned}\n"
    report += f"   Trades Taken: {trades_taken}\n\n"
    if trade_section:
        report += "💼 TRADE DETAILS:\n" + trade_section + "\n"
    report += "📊 DAILY SUMMARY:\n"
    report += f"   Number of Trades Placed | Wins: {wins} | Losses: {losses}\n"
    report += f"   Win Rate: {win_rate:.2f}%\n"
    report += f"   Daily P&L: {daily_pnl}\n"
    report += f"   {profitable_day}\n"
    return report

# Call at end: with open(f'daily_report_{current_date}.txt', 'w') as f: f.write(generate_daily_report(trade_log, df))