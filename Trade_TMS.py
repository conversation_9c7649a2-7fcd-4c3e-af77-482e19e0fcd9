MIN_CAPTURE_POINTS = 60

# Trailing stop function
def manage_trade(entry_price, bias, current_price, initial_sl, initial_tp):
    profit_points = (current_price - entry_price) if bias == 'buy' else (entry_price - current_price)
    
    # At initial reward (20 or 10 points)
    if profit_points >= initial_tp - entry_price:  # Adjust for bias
        sl = entry_price  # Breakeven
    
    # To 60 points: Hold with breakeven SL
    if profit_points >= 60:
        # Trail by 2 points
        trail_amount = 2
        if bias == 'buy':
            sl = current_price - (initial_sl - entry_price) - trail_amount  # Adjust dynamically
        else:
            sl = current_price + (entry_price - initial_sl) + trail_amount
    
    # For 5:10 RR specific
    if initial_tp - entry_price == EQH_EQL_REWARD_POINTS:
        if profit_points >= initial_tp - entry_price + 5:
            # Trail further by 2 points steps
    
    # Exit if SL hit or manual
    if (bias == 'buy' and current_price < sl) or (bias == 'sell' and current_price > sl):
        return 'exit', profit_points  # Or loss
    
    return 'hold', profit_points

# Backtest loop example
def backtest(df):
    positions = []  # List of dicts: {'entry': price, 'bias': 'buy', 'sl': sl, 'tp': tp}
    for i in range(len(df)):
        row = df.iloc[i]
        detection = detect_eqh_eql(df.iloc[:i+1])  # Rolling
        if detection:
            bias = get_trade_bias_on_eqh_eql(detection, get_market_sentiment(...), ...)
            if bias and is_interval_start(row['timestamp']) and avoid_against_interchange(...):
                entry = row['close']
                sl, tp = calculate_sl_tp(entry, bias)  # Or default if not EQH/EQL
                positions.append({'entry': entry, 'bias': bias, 'sl': sl, 'tp': tp})
        
        # Manage open positions
        for pos in positions:
            status, pnl = manage_trade(pos['entry'], pos['bias'], row['close'], pos['sl'], pos['tp'])
            if status == 'exit':
                # Log alert/P&L
                print(f"Alert: Exit {pos['bias']} at {row['close']}, PNL: {pnl}")
                positions.remove(pos)